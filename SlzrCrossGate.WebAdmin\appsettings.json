{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning", "SlzrCrossGate.WebAdmin.Middleware.GlobalExceptionHandlingMiddleware": "Information", "SlzrCrossGate.WebAdmin.Filters.ActionLoggingFilter": "Information", "SlzrCrossGate.WebAdmin.Controllers": "Information"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Server=mysql-server;Port=3306;Database=tcpserver;User=root;Password=your-secure-password;SslMode=Required;AllowLoadLocalInfile=true;"}, "DatabaseProvider": "MySql", "RabbitMQ": {"HostName": "localhost", "UserName": "guest", "Password": "guest", "Port": 5672}, "TerminalLogProcessing": {"Enabled": true, "BatchSize": 100, "BatchIntervalSeconds": 10, "EnableDebugLogging": false, "Exchange": "SlzrCrossGate.Data", "Queue": "SlzrCrossGate.Data.Queue.TerminalLog", "RoutingKey": "Tcp.city.#"}, "Jwt": {"Key": "YourSecretKeyHere12345678901234567890", "Issuer": "WebAdmin", "Audience": "WebAdmin", "ExpiresInHours": 24}, "SessionTimeout": {"Minutes": 120, "WarningMinutes": 10}, "Wechat": {"AppId": "wx123456789abcdef", "AppSecret": "your_app_secret_here", "RedirectUrl": "https://localhost:7296/api/auth/wechat-callback", "QrCodeExpiryMinutes": 5}, "FileService": {"DefaultStorageType": "Local", "LocalFilePath": "D:\\", "MinIO": {"Endpoint": "minio.example.com", "AccessKey": "your-access-key", "SecretKey": "your-secret-key", "BucketName": "your-bucket-name"}}}