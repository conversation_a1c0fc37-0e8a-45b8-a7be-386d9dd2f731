# 多数据库迁移快速参考

## 🚀 常用命令

### MySQL环境（日常开发）
```bash
# 配置: appsettings.json -> "DatabaseProvider": "MySql"

  "ConnectionStrings": {
    "DefaultConnection": "Server=mysql-server;Port=3306;Database=tcpserver;User=root;Password=your-secure-password;SslMode=Required;AllowLoadLocalInfile=true;"
  },
  "DatabaseProvider": "MySql",



# 生成迁移
dotnet ef migrations add FeatureName --project SlzrCrossGate.Migrations.MySql --startup-project SlzrCrossGate.WebAdmin

# 应用迁移
dotnet ef database update --project SlzrCrossGate.Migrations.MySql --startup-project SlzrCrossGate.WebAdmin

# 查看迁移状态
dotnet ef migrations list --project SlzrCrossGate.Migrations.MySql --startup-project SlzrCrossGate.WebAdmin
```

### SQL Server环境
```bash
# 配置: appsettings.json -> "DatabaseProvider": "SqlServer"

  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=TcpserverTms;User Id=sa;Password=***********;TrustServerCertificate=true;MultipleActiveResultSets=true"
  },
  "DatabaseProvider": "SqlServer",


# 生成迁移
dotnet ef migrations add FeatureName --project SlzrCrossGate.Migrations.SqlServer --startup-project SlzrCrossGate.WebAdmin

# 应用迁移
dotnet ef database update --project SlzrCrossGate.Migrations.SqlServer --startup-project SlzrCrossGate.WebAdmin

# 查看迁移状态
dotnet ef migrations list --project SlzrCrossGate.Migrations.SqlServer --startup-project SlzrCrossGate.WebAdmin
```

## 📋 配置模板

### MySQL配置
```json
{
  "DatabaseProvider": "MySql",
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=TcpserverTms;User=root;Password=your_password;CharSet=utf8mb4;"
  }
}
```

### SQL Server配置
```json
{
  "DatabaseProvider": "SqlServer",
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=TcpserverTms;User Id=sa;Password=your_password;TrustServerCertificate=true;MultipleActiveResultSets=true;"
  }
}
```

## 🔄 典型工作流程

### 1. 新功能开发
```bash
# 1. 在MySQL环境开发
# 2. 生成MySQL迁移
dotnet ef migrations add AddNewFeature --project SlzrCrossGate.Migrations.MySql --startup-project SlzrCrossGate.WebAdmin

# 3. 测试MySQL迁移
dotnet ef database update --project SlzrCrossGate.Migrations.MySql --startup-project SlzrCrossGate.WebAdmin

# 4. 切换到SQL Server配置
# 5. 生成SQL Server迁移
dotnet ef migrations add AddNewFeature --project SlzrCrossGate.Migrations.SqlServer --startup-project SlzrCrossGate.WebAdmin

# 6. 测试SQL Server迁移
dotnet ef database update --project SlzrCrossGate.Migrations.SqlServer --startup-project SlzrCrossGate.WebAdmin
```

### 2. 生产部署
```bash
# MySQL生产环境
dotnet ef database update --project SlzrCrossGate.Migrations.MySql --startup-project SlzrCrossGate.WebAdmin

# SQL Server生产环境
dotnet ef database update --project SlzrCrossGate.Migrations.SqlServer --startup-project SlzrCrossGate.WebAdmin
```

## ⚠️ 重要提醒

- ✅ **MySQL生产环境安全**: 现有MySQL环境完全不受影响
- ✅ **自动化**: 无需手动修改迁移文件，EF Core自动生成正确语法
- ✅ **隔离**: 两个数据库的迁移历史完全独立
- ⚠️ **配置检查**: 执行迁移前务必检查`DatabaseProvider`配置
- ⚠️ **备份**: 重要变更前建议备份数据库

## 🆘 紧急命令

### 回滚迁移
```bash
# 回滚到指定迁移
dotnet ef database update MigrationName --project SlzrCrossGate.Migrations.MySql --startup-project SlzrCrossGate.WebAdmin

# 回滚到初始状态
dotnet ef database update 0 --project SlzrCrossGate.Migrations.MySql --startup-project SlzrCrossGate.WebAdmin
```

### 删除最后一个迁移（未应用时）
```bash
dotnet ef migrations remove --project SlzrCrossGate.Migrations.MySql --startup-project SlzrCrossGate.WebAdmin
```

### 生成SQL脚本（不直接执行）
```bash
dotnet ef migrations script --project SlzrCrossGate.Migrations.MySql --startup-project SlzrCrossGate.WebAdmin
```
