# 删除SQL Server数据库

$connectionString = "Server=localhost;Database=master;User Id=sa;Password=***********;TrustServerCertificate=true"

try {
    $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
    $connection.Open()
    
    Write-Host "Setting database to single user mode..."
    $command = New-Object System.Data.SqlClient.SqlCommand("ALTER DATABASE TcpserverTms SET SINGLE_USER WITH ROLLBACK IMMEDIATE", $connection)
    $command.ExecuteNonQuery()

    Write-Host "Dropping database TcpserverTms..."
    $command = New-Object System.Data.SqlClient.SqlCommand("DROP DATABASE TcpserverTms", $connection)
    $command.ExecuteNonQuery()
    
    Write-Host "Database dropped successfully!"
    
    $connection.Close()
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}
