{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "ConnectionStrings": {"DefaultConnection": "Server=localhost;Database=TcpserverTms;User Id=sa;Password=***********;TrustServerCertificate=true;MultipleActiveResultSets=true"}, "DatabaseProvider": "SqlServer", "RabbitMQ": {"HostName": "rabbitmq-server", "UserName": "guest", "Password": "guest", "Port": 5672}, "Jwt": {"Key": "your-secure-key-replace-this-with-strong-key", "Issuer": "slzr-cn.com", "Audience": "WebAdmin", "ExpiresInHours": 24}, "Wechat": {"AppId": "your-wechat-appid", "AppSecret": "your-wechat-secret", "RedirectUrl": "https://your-domain.com/api/auth/wechat-callback", "QrCodeExpiryMinutes": 5}, "FileService": {"DefaultStorageType": "Local", "LocalFilePath": "/app/storage/files", "MinIO": {"Endpoint": "minio.example.com", "AccessKey": "your-access-key", "SecretKey": "your-secret-key", "BucketName": "your-bucket-name"}}, "Kestrel": {"EndpointDefaults": {"Protocols": "Http1AndHttp2"}, "Endpoints": {"Http": {"Url": "http://0.0.0.0:80"}}}}