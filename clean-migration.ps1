# 清理MySQL迁移文件，使其兼容SQL Server

$file = "SlzrCrossGate.Core/Migrations/20250815143000_InitialCreate_SqlServer.cs"

Write-Host "Cleaning migration file: $file"

$content = Get-Content $file -Raw

# 移除MySQL注解
$content = $content -replace '\s*\.Annotation\("MySql:CharSet", "utf8mb4"\)', ''

# 替换数据类型
$content = $content -replace 'type: "tinyint\(1\)"', 'type: "bit"'
$content = $content -replace 'type: "longtext"', 'type: "nvarchar(max)"'

Set-Content $file $content -Encoding UTF8

Write-Host "Cleaning completed!"
