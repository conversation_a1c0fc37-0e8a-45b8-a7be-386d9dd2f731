# 简化的SQL Server连接测试

$connectionString = "Server=localhost;Database=master;User Id=sa;Password=***********;TrustServerCertificate=true;Connection Timeout=5;"

Write-Host "Testing SQL Server connection..." -ForegroundColor Yellow

try {
    $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
    $connection.Open()
    
    Write-Host "Connection successful!" -ForegroundColor Green
    
    # Get SQL Server version
    $command = New-Object System.Data.SqlClient.SqlCommand("SELECT @@VERSION", $connection)
    $version = $command.ExecuteScalar()
    Write-Host "SQL Server Version: $version" -ForegroundColor Cyan
    
    # Check if TcpserverTms database exists
    $checkDbCommand = New-Object System.Data.SqlClient.SqlCommand("SELECT COUNT(*) FROM sys.databases WHERE name = 'TcpserverTms'", $connection)
    $dbExists = $checkDbCommand.ExecuteScalar()
    
    if ($dbExists -eq 0) {
        Write-Host "Database 'TcpserverTms' does not exist. Creating..." -ForegroundColor Yellow
        $createDbCommand = New-Object System.Data.SqlClient.SqlCommand("CREATE DATABASE TcpserverTms", $connection)
        $createDbCommand.ExecuteNonQuery()
        Write-Host "Database 'TcpserverTms' created successfully!" -ForegroundColor Green
    } else {
        Write-Host "Database 'TcpserverTms' exists." -ForegroundColor Green
    }
    
    $connection.Close()
    
} catch {
    Write-Host "Connection failed!" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "Test completed." -ForegroundColor Gray
