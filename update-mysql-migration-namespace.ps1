# 更新MySQL迁移文件的命名空间

$migrationFiles = Get-ChildItem "SlzrCrossGate.Migrations.MySql/Migrations/*.cs"

foreach ($file in $migrationFiles) {
    Write-Host "Updating namespace in: $($file.Name)"
    
    $content = Get-Content $file.FullName -Raw
    
    # 更新命名空间
    $content = $content -replace 'namespace SlzrCrossGate\.Core\.Migrations', 'namespace SlzrCrossGate.Migrations.MySql.Migrations'
    
    # 更新using语句
    $content = $content -replace 'using SlzrCrossGate\.Core\.Database;', 'using SlzrCrossGate.Core.Database;'
    
    Set-Content $file.FullName $content -Encoding UTF8
}

Write-Host "MySQL migration namespaces updated!"
