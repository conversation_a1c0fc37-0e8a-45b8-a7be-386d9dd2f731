# 简单的WebAdmin测试

Write-Host "Testing WebAdmin with SQL Server..." -ForegroundColor Yellow

Set-Location "e:\Coding\Solution\通讯程序\SlzrCrossGate\SlzrCrossGate.WebAdmin"

Write-Host "Building project..." -ForegroundColor Yellow
dotnet build

if ($LASTEXITCODE -eq 0) {
    Write-Host "Build successful!" -ForegroundColor Green
    Write-Host "You can now run: dotnet run --launch-profile https" -ForegroundColor Cyan
} else {
    Write-Host "Build failed!" -ForegroundColor Red
}
