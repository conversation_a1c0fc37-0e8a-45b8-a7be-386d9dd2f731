# 测试WebAdmin SQL Server支持

Write-Host "Testing WebAdmin with SQL Server..." -ForegroundColor Yellow

# 确保在正确的目录
Set-Location "e:\Coding\Solution\通讯程序\SlzrCrossGate\SlzrCrossGate.WebAdmin"

Write-Host "Current directory: $(Get-Location)" -ForegroundColor Cyan

# 检查配置文件
$config = Get-Content "appsettings.json" | ConvertFrom-Json
Write-Host "Database Provider: $($config.DatabaseProvider)" -ForegroundColor Cyan
Write-Host "Connection String: $($config.ConnectionStrings.DefaultConnection)" -ForegroundColor Cyan

# 尝试构建项目
Write-Host "`nBuilding project..." -ForegroundColor Yellow
$buildResult = dotnet build 2>&1
if ($LASTEXITCODE -eq 0) {
    Write-Host "Build successful!" -ForegroundColor Green
} else {
    Write-Host "Build failed!" -ForegroundColor Red
    Write-Host $buildResult -ForegroundColor Red
    exit 1
}

# 尝试运行项目（短时间）
Write-Host "`nStarting WebAdmin..." -ForegroundColor Yellow
$process = Start-Process -FilePath "dotnet" -ArgumentList "run --launch-profile https" -PassThru -NoNewWindow

# 等待几秒钟让应用启动
Start-Sleep -Seconds 10

# 检查进程是否还在运行
if ($process.HasExited) {
    Write-Host "Process exited with code: $($process.ExitCode)" -ForegroundColor Red
} else {
    Write-Host "WebAdmin is running (PID: $($process.Id))" -ForegroundColor Green
    
    # 尝试访问健康检查端点
    try {
        $response = Invoke-WebRequest -Uri "https://localhost:7296/health" -SkipCertificateCheck -TimeoutSec 5
        Write-Host "Health check response: $($response.StatusCode)" -ForegroundColor Green
    } catch {
        Write-Host "Health check failed: $($_.Exception.Message)" -ForegroundColor Yellow
    }
    
    # 停止进程
    Write-Host "Stopping WebAdmin..." -ForegroundColor Yellow
    $process.Kill()
    $process.WaitForExit()
    Write-Host "WebAdmin stopped." -ForegroundColor Green
}

Write-Host "`nTest completed." -ForegroundColor Gray
