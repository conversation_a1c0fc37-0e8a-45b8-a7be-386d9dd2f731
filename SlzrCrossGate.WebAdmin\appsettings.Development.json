{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Information", "Microsoft.EntityFrameworkCore.Database.Command": "Information", "SlzrCrossGate.WebAdmin.Middleware.GlobalExceptionHandlingMiddleware": "Debug", "SlzrCrossGate.WebAdmin.Filters.ActionLoggingFilter": "Debug", "SlzrCrossGate.WebAdmin.Controllers": "Debug"}}, "ConnectionStrings": {"DefaultConnection": "Server=mysql-server;Port=3306;Database=tcpserver;User=root;Password=your-secure-password;SslMode=Required;AllowLoadLocalInfile=true;"}, "DatabaseProvider": "MySql"}