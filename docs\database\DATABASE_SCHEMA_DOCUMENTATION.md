# 数据库表结构说明文档

## 📋 概述

本文档详细说明了 SlzrCrossGate 项目中 TcpDbContext 涉及的所有数据库表结构。项目使用 MySQL 数据库，采用 Entity Framework Core 进行数据访问。

## 📊 表分类

### 1. Identity 认证表（ASP.NET Core Identity）
- ApplicationUsers - 用户表
- ApplicationRoles - 角色表
- UserRoles - 用户角色关联表
- UserClaims - 用户声明表
- UserLogins - 用户登录表
- UserTokens - 用户令牌表
- RoleClaims - 角色声明表

### 2. 业务核心表
- Merchants - 商户表
- Terminals - 终端表
- TerminalStatus - 终端状态表
- TerminalEvents - 终端事件表
- TerminalLogs - 终端日志表
- FileVers - 文件版本表
- FileTypes - 文件类型表
- FilePublishs - 文件发布表
- FilePublishHistories - 文件发布历史表
- ScheduledFilePublishs - 预约文件发布表
- UploadFiles - 上传文件表
- TerminalFileUploads - 终端文件上传表
- MsgTypes - 消息类型表
- MsgContents - 消息内容表
- MsgBoxes - 消息盒子表
- ConsumeData - 消费数据表
- UnionPayTerminalKeys - 银联终端密钥表
- IncrementContents - 增量内容表
- MerchantDictionaries - 商户字典表
- LinePriceInfos - 线路票价信息表
- LinePriceInfoVersions - 线路票价信息版本表
- FareDiscountSchemes - 票价折扣方案表
- FareDiscountSchemeVersions - 票价折扣方案版本表
- VehicleInfos - 车辆信息表

### 3. 系统管理表
- SystemSettings - 系统设置表
- MenuGroups - 菜单组表
- MenuItems - 菜单项表
- FeatureConfigs - 功能配置表
- RoleFeaturePermissions - 角色功能权限表

### 4. 审计日志表
- LoginLogs - 登录日志表
- PasswordChangeLogs - 密码变更日志表
- OperationLogs - 操作日志表

## 🔍 详细表结构

### ApplicationUsers（用户表）

扩展自 ASP.NET Core Identity 的 IdentityUser，包含系统特定字段。

| 字段名 | 类型 | 长度 | 必填 | 默认值 | 说明 |
|--------|------|------|------|--------|------|
| Id | string | 128 | ✓ | - | 用户ID（主键） |
| UserName | string | 128 | ✓ | - | 用户名 |
| NormalizedUserName | string | 128 | - | - | 标准化用户名 |
| Email | string | 128 | - | - | 邮箱 |
| NormalizedEmail | string | 128 | - | - | 标准化邮箱 |
| RealName | string | - | - | - | 真实姓名 |
| MerchantID | string | - | - | - | 商户ID |
| CreateTime | DateTime | - | ✓ | DateTime.Now | 创建时间 |
| IsDeleted | bool | - | ✓ | false | 是否删除 |
| TwoFactorSecretKey | string | - | - | - | 双因素认证密钥 |
| IsTwoFactorRequired | bool | - | ✓ | false | 是否强制双因素认证 |
| TwoFactorEnabledDate | DateTime? | - | - | - | 双因素认证启用时间 |
| FailedTwoFactorAttempts | int? | - | - | - | 双因素认证失败次数 |
| LastFailedTwoFactorAttempt | DateTime? | - | - | - | 最后失败时间 |
| WechatOpenId | string | - | - | - | 微信OpenID |
| WechatUnionId | string | - | - | - | 微信UnionID |
| WechatNickname | string | - | - | - | 微信昵称 |
| WechatBindTime | DateTime? | - | - | - | 微信绑定时间 |
| LastPasswordChangeTime | DateTime? | - | - | - | 最后密码更改时间 |
| RequirePasswordChange | bool | - | ✓ | false | 是否需要强制更改密码 |

### ApplicationRoles（角色表）

扩展自 ASP.NET Core Identity 的 IdentityRole。

| 字段名 | 类型 | 长度 | 必填 | 默认值 | 说明 |
|--------|------|------|------|--------|------|
| Id | string | 128 | ✓ | - | 角色ID（主键） |
| Name | string | 128 | ✓ | - | 角色名称 |
| NormalizedName | string | 128 | - | - | 标准化角色名称 |
| IsSysAdmin | bool | - | ✓ | false | 是否系统管理员 |

### Merchants（商户表）

| 字段名 | 类型 | 长度 | 必填 | 默认值 | 说明 |
|--------|------|------|------|--------|------|
| MerchantID | string | 8 | ✓ | - | 商户ID（主键） |
| Name | string | 100 | - | - | 商户名称 |
| CompanyName | string | 100 | - | - | 公司名称 |
| ContactPerson | string | 100 | - | - | 联系人 |
| ContactInfo | string | 100 | - | - | 联系信息 |
| Remark | string | 200 | - | - | 备注 |
| Operator | string | 50 | - | - | 运维人员 |
| AutoRegister | bool | - | ✓ | false | 自动注册 |
| IsDelete | bool | - | ✓ | false | 是否删除 |

### Terminals（终端表）

| 字段名 | 类型 | 长度 | 必填 | 默认值 | 说明 |
|--------|------|------|------|--------|------|
| ID | string | 20 | ✓ | - | 系统唯一编号（主键） |
| MerchantID | string | 8 | ✓ | - | 商户编号 |
| MachineID | string | 8 | ✓ | - | 终端唯一码 |
| DeviceNO | string | 8 | ✓ | - | 设备编号/车辆编号 |
| LineNO | string | 8 | ✓ | - | 线路号 |
| TerminalType | string | - | ✓ | - | 设备类型 |
| CreateTime | DateTime | - | ✓ | - | 创建时间 |
| IsDeleted | bool | - | ✓ | false | 软删除标记 |

**索引：**
- IX_Terminals_MerchantID_MachineID
- IX_Terminals_MerchantID_DeviceNO

### TerminalStatus（终端状态表）

| 字段名 | 类型 | 长度 | 必填 | 默认值 | 说明 |
|--------|------|------|------|--------|------|
| ID | string | 20 | ✓ | - | 终端ID（主键，外键） |
| LastActiveTime | DateTime | - | ✓ | DateTime.MinValue | 最后活跃时间 |
| ActiveStatus | DeviceActiveStatus | - | ✓ | Active | 活跃状态 |
| LoginInTime | DateTime | - | ✓ | DateTime.MinValue | 登录时间 |
| LoginOffTime | DateTime | - | ✓ | DateTime.MinValue | 登出时间 |
| Token | string | 200 | - | - | 令牌 |
| ConnectionProtocol | string | 20 | - | - | 连接协议 |
| EndPoint | string | 200 | - | - | 端点 |
| FileVersions | string | 2000 | - | "" | 文件版本（JSON） |
| Properties | string | 2000 | - | "" | 属性（JSON） |

### FileVers（文件版本表）

| 字段名 | 类型 | 长度 | 必填 | 默认值 | 说明 |
|--------|------|------|------|--------|------|
| ID | int | - | ✓ | - | 自增ID（主键） |
| MerchantID | string | 8 | ✓ | - | 商户ID |
| FileTypeID | string | 3 | ✓ | - | 文件类型ID |
| FilePara | string | 8 | ✓ | - | 文件参数 |
| FileFullType | string | 11 | ✓ | - | 完整文件类型 |
| Ver | string | 4 | ✓ | - | 版本号 |
| CreateTime | DateTime | - | ✓ | DateTime.Now | 创建时间 |
| UpdateTime | DateTime | - | ✓ | DateTime.Now | 更新时间 |
| UploadFileID | string | 32 | ✓ | - | 上传文件ID |
| Remarks | string | 500 | - | - | 备注信息 |

**索引：**
- IX_FileVers_MerchantID_FileFullType_Ver
- IX_FileVers_MerchantID_FileTypeID_FilePara_Ver
- IX_FileVers_CreateTime（降序）

### FilePublishs（文件发布表）

| 字段名 | 类型 | 长度 | 必填 | 默认值 | 说明 |
|--------|------|------|------|--------|------|
| ID | int | - | ✓ | - | 自增ID（主键） |
| MerchantID | string | 8 | ✓ | - | 商户ID |
| FileTypeID | string | 3 | ✓ | - | 文件类型ID |
| FilePara | string | 8 | ✓ | - | 文件参数 |
| FileFullType | string | 11 | ✓ | - | 完整文件类型 |
| Ver | string | 4 | ✓ | - | 版本号 |
| FileSize | int | - | ✓ | - | 文件大小 |
| Crc | string | 8 | ✓ | - | CRC校验值 |
| PublishTime | DateTime | - | ✓ | - | 发布时间 |
| FileVerID | int | - | ✓ | - | 文件版本ID |
| UploadFileID | string | 32 | ✓ | - | 上传文件ID |
| Operator | string | 20 | - | - | 操作人员 |
| PublishType | PublishTypeOption | - | ✓ | - | 发布类型 |
| PublishTarget | string | 100 | ✓ | - | 发布目标 |
| Remark | string | 200 | - | - | 发布备注 |

**索引：**
- UK_FilePublish_MerchantID_FileTypeID_FilePara_PublishType_PublishTarget（唯一）
- UK_FilePublish_MerchantID_FileFullType_PublishType_PublishTarget（唯一）
- IX_FilePublish_MerchantID_FileTypeID_PublishTime
- IX_FilePublish_PublishTime（降序）

### ScheduledFilePublishs（预约文件发布表）

| 字段名 | 类型 | 长度 | 必填 | 默认值 | 说明 |
|--------|------|------|------|--------|------|
| ID | int | - | ✓ | - | 自增ID（主键） |
| FileVersionID | int | - | ✓ | - | 文件版本ID |
| MerchantID | string | 8 | ✓ | - | 商户ID |
| FileTypeID | string | 3 | ✓ | - | 文件类型ID |
| FilePara | string | 8 | ✓ | - | 文件参数 |
| FileFullType | string | 11 | ✓ | - | 完整文件类型 |
| Ver | string | 4 | ✓ | - | 版本号 |
| PublishType | PublishTypeOption | - | ✓ | - | 发布类型 |
| PublishTarget | string | 100 | ✓ | - | 发布目标 |
| ScheduledTime | DateTime | - | ✓ | - | 预约发布时间 |
| Status | ScheduledPublishStatus | - | ✓ | Pending | 预约状态 |
| CreatedTime | DateTime | - | ✓ | DateTime.Now | 创建时间 |
| CreatedBy | string | 20 | ✓ | - | 创建人 |
| Remarks | string | 500 | - | - | 备注 |

**索引：**
- IX_ScheduledFilePublish_MerchantID_Status
- IX_ScheduledFilePublish_Status_ScheduledTime
- IX_ScheduledFilePublish_MerchantID_CreatedTime（降序）

### MsgTypes（消息类型表）

| 字段名 | 类型 | 长度 | 必填 | 默认值 | 说明 |
|--------|------|------|------|--------|------|
| ID | string | 4 | ✓ | - | 消息类型ID（主键） |
| MerchantID | string | 8 | ✓ | - | 商户ID（主键） |
| Name | string | 50 | - | - | 消息类型名称 |
| CodeType | MessageCodeType | - | ✓ | HEX | 消息编码类型 |
| ExampleMessage | string | 1000 | - | - | 示例消息 |
| Description | string | 500 | - | - | 描述 |

### MsgContents（消息内容表）

| 字段名 | 类型 | 长度 | 必填 | 默认值 | 说明 |
|--------|------|------|------|--------|------|
| ID | int | - | ✓ | - | 自增ID（主键） |
| MerchantID | string | 8 | ✓ | - | 商户ID |
| MsgTypeID | string | 4 | ✓ | - | 消息类型ID |
| Content | string | 500 | ✓ | - | 消息内容 |
| CreateTime | DateTime | - | ✓ | - | 创建时间 |
| Remark | string | 200 | - | - | 备注 |
| Operator | string | 20 | - | - | 操作人员 |

**索引：**
- IX_MsgContents_MerchantID_CreateTime（降序）

### MsgBoxes（消息盒子表）

| 字段名 | 类型 | 长度 | 必填 | 默认值 | 说明 |
|--------|------|------|------|--------|------|
| ID | int | - | ✓ | - | 自增ID（主键） |
| MerchantID | string | 8 | ✓ | - | 商户ID |
| MsgContentID | int | - | ✓ | - | 消息内容ID |
| TerminalID | string | 20 | ✓ | - | 接收设备ID |
| Status | MessageStatus | - | ✓ | - | 消息状态 |
| SendTime | DateTime | - | ✓ | - | 发送时间 |
| ReadTime | DateTime? | - | - | - | 读取时间 |
| ReplyTime | DateTime? | - | - | - | 回复时间 |
| ReplyCode | string | 20 | - | - | 回复代码 |
| ReplyContent | string | 200 | - | - | 回复内容 |

**索引：**
- IX_MsgBoxes_MerchantID_Status_TerminalID_SendTime
- IX_MsgBoxes_SendTime（降序）

### ConsumeData（消费数据表）

| 字段名 | 类型 | 长度 | 必填 | 默认值 | 说明 |
|--------|------|------|------|--------|------|
| Id | long | - | ✓ | - | 自增ID（主键） |
| MerchantID | string | 8 | - | - | 商户ID |
| MachineNO | string | 8 | - | - | 设备编号 |
| MachineID | string | 8 | - | - | 设备序列号 |
| PsamNO | string | 12 | - | - | PSAM卡号 |
| Buffer | byte[] | 2500 | ✓ | - | 交易数据缓冲区 |
| ReceiveTime | DateTime | - | ✓ | - | 接收时间 |

**索引：**
- IX_ConsumeData_ReceiveTime（降序）

### TerminalLogs（终端日志表）

| 字段名 | 类型 | 长度 | 必填 | 默认值 | 说明 |
|--------|------|------|------|--------|------|
| ID | int | - | ✓ | - | 自增ID（主键） |
| MerchantID | string | 8 | - | - | 商户ID |
| LogType | int? | - | - | - | 记录类型 |
| SetMethod | int? | - | - | - | 设置方式 |
| CardNO | string | 20 | - | - | 卡号 |
| MachineID | string | 20 | - | - | 设备序列号 |
| MachineNO | string | 20 | - | - | 设备编号 |
| LineNO | string | 20 | - | - | 线路号 |
| Fare | int? | - | - | - | 票价（分） |
| DriverCardNO | string | 20 | - | - | 司机卡号 |
| LogTime | DateTime? | - | - | - | 记录时间 |
| UploadTime | DateTime? | - | - | - | 上传时间 |

**索引：**
- IX_TerminalLogs_MerchantID_LogTime（降序）
- IX_TerminalLogs_LogType_LogTime（降序）
- IX_TerminalLogs_MachineID_LogTime（降序）
- IX_TerminalLogs_MachineNO_LogTime（降序）
- IX_TerminalLogs_LineNO_LogTime（降序）
- IX_TerminalLogs_CardNO_LogTime（降序）
- IX_TerminalLogs_LogTime（降序）

## 📝 枚举类型

### PublishTypeOption（发布类型）
- None = 0
- Merchant = 1（商户级）
- Line = 2（线路级）
- Terminal = 3（终端级）

### DeviceActiveStatus（设备活跃状态）
- Active = 0（活跃）
- Inactive = 1（非活跃）

### MessageCodeType（消息编码类型）
- ASCII = 1
- HEX = 2

### LinePriceVersionStatus（线路价格版本状态）
- Draft = 0（草稿）
- Submitted = 1（已提交）

### FareDiscountVersionStatus（票价折扣方案版本状态）
- Draft = 0（草稿）
- Submitted = 1（已提交）

### TerminalLogType（终端日志类型）
- DriverCard = 10（司机卡）
- LineSettings = 12（线路设置）
- DeviceSettings = 16（设备设置）
- DepartureCard = 19（发车卡）
- ArrivalCard = 20（到站卡）

### TerminalLogSetMethod（终端日志设置方式）
- M1Card = 0x01（M1卡）
- CPUCard = 0x02（CPU卡）
- TransportCard = 0x0B（交通卡）
- Wireless = 0xA0（无线）
- Dispatch = 0xA1（调度）

### TerminalUploadStatus（终端上传状态）
- Uploading = 0（上传中）
- Completed = 1（已完成）
- Failed = 2（失败）
- Cancelled = 3（已取消）

### ScheduledPublishStatus（预约发布状态）
- Pending = 0（待发布）
- Published = 1（已发布）
- Cancelled = 2（已取消）
- Failed = 3（发布失败）

### MessageStatus（消息状态）
- Unread = 0（未读）
- Read = 1（已读）
- Replied = 2（已回复）

## 🏢 业务扩展表

### UnionPayTerminalKeys（银联终端密钥表）

| 字段名 | 类型 | 长度 | 必填 | 默认值 | 说明 |
|--------|------|------|------|--------|------|
| ID | int | - | ✓ | - | 自增ID（主键） |
| MerchantID | string | 8 | ✓ | - | 商户号 |
| UP_MerchantID | string | 15 | ✓ | - | 银联商户号 |
| UP_TerminalID | string | 8 | ✓ | - | 银联终端号 |
| UP_Key | string | 32 | ✓ | - | 银联终端密钥 |
| UP_MerchantName | string | 50 | - | - | 银联商户名称 |
| IsInUse | bool | - | ✓ | false | 是否被使用（绑定） |
| MachineID | string | 8 | - | - | 被使用的设备ID |
| LineID | string | 8 | - | - | 被使用的设备所在线路ID |
| MachineNO | string | 8 | - | - | 被使用的设备编号 |
| CreatedAt | DateTime | - | ✓ | - | 创建时间 |
| UpdatedAt | DateTime | - | ✓ | - | 更新时间 |

**索引：**
- IX_UnionPayTerminalKeys_MerchantID_MachineID
- IX_UnionPayTerminalKeys_MerchantID_IsInUse
- UK_UnionPayTerminalKeys_UP_MerchantID_UP_TerminalID（唯一）

### LinePriceInfos（线路票价信息表）

| 字段名 | 类型 | 长度 | 必填 | 默认值 | 说明 |
|--------|------|------|------|--------|------|
| ID | int | - | ✓ | - | 自增ID（主键） |
| MerchantID | string | 8 | ✓ | - | 商户ID |
| LineNumber | string | 4 | ✓ | - | 线路编号 |
| GroupNumber | string | 2 | ✓ | - | 子线路号（组号） |
| LineName | string | 100 | ✓ | - | 线路名称 |
| Fare | int | - | ✓ | - | 票价（分） |
| IsActive | bool | - | ✓ | true | 是否启用 |
| CreateTime | DateTime | - | ✓ | DateTime.Now | 创建时间 |
| UpdateTime | DateTime | - | ✓ | DateTime.Now | 更新时间 |
| Creator | string | 50 | - | - | 创建者 |
| Updater | string | 50 | - | - | 更新者 |
| CurrentVersion | string | 4 | - | "" | 当前最新版本号 |
| CurrentFareDiscountSchemeID | int? | - | - | - | 当前使用的票价折扣方案ID |
| CurrentSchemeName | string | 100 | - | - | 当前使用的票价折扣方案名称 |

**索引：**
- UK_LinePriceInfo_MerchantID_LineNumber_GroupNumber（唯一）

### FareDiscountSchemes（票价折扣方案表）

| 字段名 | 类型 | 长度 | 必填 | 默认值 | 说明 |
|--------|------|------|------|--------|------|
| ID | int | - | ✓ | - | 自增ID（主键） |
| MerchantID | string | 8 | ✓ | - | 商户ID |
| SchemeCode | string | 50 | ✓ | - | 方案编号 |
| SchemeName | string | 100 | ✓ | - | 方案名称 |
| Description | string | 500 | - | - | 方案描述 |
| ExtraParamsJson | text | - | - | - | 线路额外参数JSON |
| CardDiscountInfoJson | text | - | - | - | 卡类参数信息JSON |
| IsActive | bool | - | ✓ | true | 是否启用 |
| CreateTime | DateTime | - | ✓ | DateTime.Now | 创建时间 |
| UpdateTime | DateTime | - | ✓ | DateTime.Now | 更新时间 |
| Creator | string | 50 | - | - | 创建者 |
| Updater | string | 50 | - | - | 更新者 |
| UsageCount | int | - | ✓ | 0 | 使用次数统计 |
| LastUsedTime | DateTime? | - | - | - | 最后使用时间 |
| CurrentVersion | string | 4 | - | - | 当前最新版本号 |
| CurrentFilePara | string | 50 | - | "" | 当前文件参数 |
| CurrentFileVerID | int? | - | - | - | 当前版本对应的文件版本ID |

**索引：**
- UK_FareDiscountScheme_MerchantID_SchemeCode（唯一）
- IX_FareDiscountScheme_MerchantID_IsActive
- IX_FareDiscountScheme_MerchantID_UsageCount

### VehicleInfos（车辆信息表）

| 字段名 | 类型 | 长度 | 必填 | 默认值 | 说明 |
|--------|------|------|------|--------|------|
| ID | int | - | ✓ | - | 自增ID（主键） |
| MerchantID | string | 8 | ✓ | - | 商户ID |
| DeviceNO | string | 8 | ✓ | - | 设备编号 |
| LicensePlate | string | 20 | ✓ | - | 车牌号 |
| VehicleType | string | 50 | - | - | 车辆类型 |
| Brand | string | 50 | - | - | 品牌 |
| Model | string | 50 | - | - | 型号 |
| Color | string | 30 | - | - | 颜色 |
| VIN | string | 50 | - | - | 车架号 |
| EngineNumber | string | 50 | - | - | 发动机号 |
| RegistrationDate | DateTime? | - | - | - | 注册日期 |
| ExpiryDate | DateTime? | - | - | - | 到期日期 |
| DriverName | string | 50 | - | - | 司机姓名 |
| DriverPhone | string | 20 | - | - | 司机电话 |
| DriverLicense | string | 30 | - | - | 驾驶证号 |
| InsuranceCompany | string | 100 | - | - | 保险公司 |
| InsurancePolicyNumber | string | 50 | - | - | 保险单号 |
| InsuranceExpiryDate | DateTime? | - | - | - | 保险到期日期 |
| MaintenanceStatus | string | 20 | - | "Normal" | 维护状态 |
| LastMaintenanceDate | DateTime? | - | - | - | 最后维护日期 |
| NextMaintenanceDate | DateTime? | - | - | - | 下次维护日期 |
| Mileage | decimal(10,2)? | - | - | - | 里程数 |
| FuelType | string | 20 | - | - | 燃料类型 |
| Remark | text | - | - | - | 备注 |
| IsActive | bool | - | ✓ | true | 是否启用 |
| CreateTime | DateTime | - | ✓ | DateTime.Now | 创建时间 |
| UpdateTime | DateTime? | - | - | - | 更新时间 |
| Creator | string | 50 | - | - | 创建者 |
| Updater | string | 50 | - | - | 更新者 |

**索引：**
- UK_Vehicle_MerchantDevice（MerchantID, DeviceNO）（唯一）
- UK_Vehicle_MerchantLicense（MerchantID, LicensePlate）（唯一）
- IDX_Vehicle_Merchant
- IDX_Vehicle_License
- IDX_Vehicle_Driver

## 🔗 关键关系

### 外键关系
- ApplicationUsers.MerchantID → Merchants.MerchantID
- Terminals.MerchantID → Merchants.MerchantID
- TerminalStatus.ID → Terminals.ID（一对一）
- FileVers.MerchantID → Merchants.MerchantID
- FilePublishs.MerchantID → Merchants.MerchantID
- LinePriceInfo.MerchantID → Merchants.MerchantID
- VehicleInfo.MerchantID → Merchants.MerchantID
- MenuItem.MenuGroupId → MenuGroups.Id
- RoleFeaturePermission.FeatureKey → FeatureConfig.FeatureKey

### 租户隔离
大部分业务表实现了 ITenantEntity 接口，通过 MerchantID 字段实现多租户数据隔离。

## �️ 系统管理表

### SystemSettings（系统设置表）

| 字段名 | 类型 | 长度 | 必填 | 默认值 | 说明 |
|--------|------|------|------|--------|------|
| Id | int | - | ✓ | - | 自增ID（主键） |
| EnableTwoFactorAuth | bool | - | ✓ | true | 启用双因素认证 |
| ForceTwoFactorAuth | bool | - | ✓ | false | 强制双因素认证 |
| EnableWechatLogin | bool | - | ✓ | true | 启用微信登录 |
| ForcePasswordChangeDays | int | - | ✓ | 0 | 强制密码更改天数 |
| LastModified | DateTime | - | ✓ | DateTime.Now | 最后修改时间 |
| LastModifiedBy | string | - | - | - | 最后修改人 |

### MenuGroups（菜单组表）

| 字段名 | 类型 | 长度 | 必填 | 默认值 | 说明 |
|--------|------|------|------|--------|------|
| Id | int | - | ✓ | - | 自增ID（主键） |
| GroupKey | string | - | ✓ | - | 组键（唯一） |
| Title | string | - | ✓ | - | 组标题 |
| IconName | string | - | - | - | 图标名称 |
| SortOrder | int | - | ✓ | 0 | 排序顺序 |
| IsVisible | bool | - | ✓ | true | 是否可见 |
| CreatedAt | DateTime | - | ✓ | DateTime.Now | 创建时间 |
| UpdatedAt | DateTime | - | ✓ | DateTime.Now | 更新时间 |

**索引：**
- UK_MenuGroups_GroupKey（唯一）
- IX_MenuGroups_SortOrder

### MenuItems（菜单项表）

| 字段名 | 类型 | 长度 | 必填 | 默认值 | 说明 |
|--------|------|------|------|--------|------|
| Id | int | - | ✓ | - | 自增ID（主键） |
| MenuGroupId | int | - | ✓ | - | 菜单组ID（外键） |
| ItemKey | string | - | ✓ | - | 项键 |
| Title | string | - | ✓ | - | 项标题 |
| Href | string | - | - | - | 链接地址 |
| IconName | string | - | - | - | 图标名称 |
| SortOrder | int | - | ✓ | 0 | 排序顺序 |
| IsVisible | bool | - | ✓ | true | 是否可见 |
| RoleVisibility | string | - | - | - | 角色可见性 |
| CreatedAt | DateTime | - | ✓ | DateTime.Now | 创建时间 |
| UpdatedAt | DateTime | - | ✓ | DateTime.Now | 更新时间 |

**索引：**
- UK_MenuItems_MenuGroupId_ItemKey（唯一）
- IX_MenuItems_MenuGroupId_SortOrder

### FeatureConfigs（功能配置表）

| 字段名 | 类型 | 长度 | 必填 | 默认值 | 说明 |
|--------|------|------|------|--------|------|
| Id | int | - | ✓ | - | 自增ID（主键） |
| FeatureKey | string | 100 | ✓ | - | 功能键（唯一） |
| FeatureName | string | 100 | ✓ | - | 功能名称 |
| Description | string | 500 | - | - | 功能描述 |
| Category | string | 50 | ✓ | - | 功能分类 |
| RiskLevel | string | 20 | ✓ | "Low" | 风险等级 |
| IsGloballyEnabled | bool | - | ✓ | true | 是否全局启用 |
| IsSystemBuiltIn | bool | - | ✓ | false | 是否系统内置 |
| SortOrder | int | - | ✓ | 0 | 排序顺序 |
| CreatedBy | string | 50 | - | - | 创建者 |
| UpdatedBy | string | 50 | - | - | 更新者 |
| CreatedAt | DateTime | - | ✓ | CURRENT_TIMESTAMP | 创建时间 |
| UpdatedAt | DateTime | - | ✓ | CURRENT_TIMESTAMP | 更新时间 |

**索引：**
- UK_FeatureConfig_Key（FeatureKey）（唯一）
- IDX_FeatureConfig_Category

### RoleFeaturePermissions（角色功能权限表）

| 字段名 | 类型 | 长度 | 必填 | 默认值 | 说明 |
|--------|------|------|------|--------|------|
| Id | int | - | ✓ | - | 自增ID（主键） |
| RoleName | string | 50 | ✓ | - | 角色名称 |
| FeatureKey | string | 100 | ✓ | - | 功能键（外键） |
| IsAllowed | bool | - | ✓ | false | 是否允许 |
| CreatedBy | string | 50 | - | - | 创建者 |
| UpdatedBy | string | 50 | - | - | 更新者 |
| CreatedAt | DateTime | - | ✓ | CURRENT_TIMESTAMP | 创建时间 |
| UpdatedAt | DateTime | - | ✓ | CURRENT_TIMESTAMP | 更新时间 |

**索引：**
- UK_RoleFeaturePermission_RoleFeature（RoleName, FeatureKey）（唯一）
- IDX_RoleFeaturePermission_Role
- IDX_RoleFeaturePermission_Feature

## 📋 审计日志表

### LoginLogs（登录日志表）

| 字段名 | 类型 | 长度 | 必填 | 默认值 | 说明 |
|--------|------|------|------|--------|------|
| Id | int | - | ✓ | - | 自增ID（主键） |
| UserId | string | - | - | - | 用户ID |
| UserName | string | - | - | - | 用户名 |
| MerchantId | string | - | - | - | 商户ID |
| LoginType | string | - | - | - | 登录类型 |
| IpAddress | string | - | - | - | IP地址 |
| UserAgent | string | - | - | - | 用户代理 |
| IsSuccess | bool | - | ✓ | - | 是否成功 |
| FailureReason | string | - | - | - | 失败原因 |
| OperationTime | DateTime | - | ✓ | DateTime.Now | 操作时间 |

**索引：**
- IX_LoginLogs_UserId_OperationTime
- IX_LoginLogs_MerchantId_OperationTime
- IX_LoginLogs_OperationTime（降序）
- IX_LoginLogs_LoginType_OperationTime

### PasswordChangeLogs（密码变更日志表）

| 字段名 | 类型 | 长度 | 必填 | 默认值 | 说明 |
|--------|------|------|------|--------|------|
| Id | int | - | ✓ | - | 自增ID（主键） |
| UserId | string | - | - | - | 用户ID |
| UserName | string | - | - | - | 用户名 |
| MerchantId | string | - | - | - | 商户ID |
| ChangeType | string | - | - | - | 变更类型 |
| IpAddress | string | - | - | - | IP地址 |
| UserAgent | string | - | - | - | 用户代理 |
| IsSuccess | bool | - | ✓ | - | 是否成功 |
| FailureReason | string | - | - | - | 失败原因 |
| OperationTime | DateTime | - | ✓ | DateTime.Now | 操作时间 |

**索引：**
- IX_PasswordChangeLogs_UserId_OperationTime
- IX_PasswordChangeLogs_MerchantId_OperationTime
- IX_PasswordChangeLogs_OperationTime（降序）
- IX_PasswordChangeLogs_ChangeType_OperationTime

### OperationLogs（操作日志表）

| 字段名 | 类型 | 长度 | 必填 | 默认值 | 说明 |
|--------|------|------|------|--------|------|
| Id | int | - | ✓ | - | 自增ID（主键） |
| UserId | string | - | - | - | 用户ID |
| UserName | string | - | - | - | 用户名 |
| MerchantId | string | - | - | - | 商户ID |
| Module | string | - | - | - | 模块名称 |
| OperationType | string | - | - | - | 操作类型 |
| ResourceId | string | - | - | - | 资源ID |
| ResourceName | string | - | - | - | 资源名称 |
| RequestPath | string | - | - | - | 请求路径 |
| RequestMethod | string | - | - | - | 请求方法 |
| RequestParameters | text | - | - | - | 请求参数 |
| ResponseStatus | int | - | - | - | 响应状态 |
| IpAddress | string | - | - | - | IP地址 |
| UserAgent | string | - | - | - | 用户代理 |
| OperationTime | DateTime | - | ✓ | DateTime.Now | 操作时间 |

**索引：**
- IX_OperationLogs_UserId_OperationTime
- IX_OperationLogs_MerchantId_OperationTime
- IX_OperationLogs_OperationTime（降序）
- IX_OperationLogs_Module_OperationType_OperationTime
- IX_OperationLogs_RequestPath_OperationTime

## �📊 性能优化

### 索引策略
- 主键索引：所有表都有主键索引
- 外键索引：外键字段自动创建索引
- 复合索引：针对常用查询组合创建复合索引
- 覆盖索引：使用 IncludeProperties 减少回表查询

### 分区策略
- 大数据量表（如 ConsumeData、TerminalLogs）按时间分区
- 使用聚集索引优化查询性能

## 🔒 安全考虑

### 数据保护
- 敏感字段加密存储
- 软删除机制保护数据完整性
- 审计日志记录所有关键操作

### 权限控制
- 基于角色的访问控制（RBAC）
- 功能级权限控制
- 商户级数据隔离

---

**文档版本：** 1.0  
**最后更新：** 2025-08-13  
**维护人员：** 开发团队
