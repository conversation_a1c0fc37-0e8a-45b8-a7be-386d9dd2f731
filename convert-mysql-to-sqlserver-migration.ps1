# 将MySQL迁移文件转换为SQL Server兼容的迁移文件

param(
    [string]$InputFile = "SlzrCrossGate.Core/Migrations/20250815143000_InitialCreate_SqlServer.cs"
)

Write-Host "Converting MySQL migration to SQL Server compatible migration..."
Write-Host "Input file: $InputFile"

# 读取文件内容
$content = Get-Content $InputFile -Raw

# 移除MySQL特有的注解 - 多次执行以确保完全清理
for ($i = 0; $i -lt 3; $i++) {
    $content = $content -replace '\s*\.Annotation\("MySql:CharSet", "utf8mb4"\)', ''
}

# 移除数据库级别的MySQL注解
$content = $content -replace 'migrationBuilder\.AlterDatabase\(\)\s*\.Annotation\("MySql:CharSet", "utf8mb4"\);', ''

# 替换MySQL特有的数据类型
$content = $content -replace 'type: "tinyint\(1\)"', 'type: "bit"'
$content = $content -replace 'type: "longtext"', 'type: "nvarchar(max)"'
$content = $content -replace 'type: "varchar\((\d+)\)"', 'type: "nvarchar($1)"'
$content = $content -replace 'type: "datetime\(6\)"', 'type: "datetime2"'

# 清理多余的空行和逗号
$content = $content -replace ',\s*\)', ')'
$content = $content -replace '\)\s*,\s*\)', '))'

# 清理空行
$content = $content -replace '\n\s*\n\s*\n', "`n`n"

# 写回文件
Set-Content $InputFile $content -Encoding UTF8

Write-Host "Conversion completed!"
Write-Host "Please review the file for any remaining MySQL-specific syntax."
