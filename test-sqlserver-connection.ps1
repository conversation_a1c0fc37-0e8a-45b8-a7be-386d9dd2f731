# SQL Server 连接测试脚本

param(
    [string]$Server = "localhost",
    [string]$Database = "master",  # 先连接到master数据库
    [string]$UserId = "sa",
    [string]$Password = "Slzr!337326"
)

Write-Host "测试SQL Server连接..." -ForegroundColor Yellow
Write-Host "服务器: $Server" -ForegroundColor Cyan
Write-Host "数据库: $Database" -ForegroundColor Cyan
Write-Host "用户: $UserId" -ForegroundColor Cyan

try {
    # 构建连接字符串
    $connectionString = "Server=$Server;Database=$Database;User Id=$UserId;Password=$Password;TrustServerCertificate=true;Connection Timeout=5;"
    
    Write-Host "`n正在测试连接..." -ForegroundColor Yellow
    
    # 创建连接
    $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
    $connection.Open()
    
    Write-Host "✅ 连接成功!" -ForegroundColor Green
    
    # 获取SQL Server版本
    $command = New-Object System.Data.SqlClient.SqlCommand("SELECT @@VERSION", $connection)
    $version = $command.ExecuteScalar()
    Write-Host "`nSQL Server版本:" -ForegroundColor Cyan
    Write-Host $version -ForegroundColor White
    
    # 检查TcpserverTms数据库是否存在
    $checkDbCommand = New-Object System.Data.SqlClient.SqlCommand("SELECT COUNT(*) FROM sys.databases WHERE name = 'TcpserverTms'", $connection)
    $dbExists = $checkDbCommand.ExecuteScalar()
    
    if ($dbExists -eq 0) {
        Write-Host "`n⚠️  数据库 'TcpserverTms' 不存在" -ForegroundColor Yellow
        Write-Host "是否要创建数据库? (y/n): " -ForegroundColor Yellow -NoNewline
        $createDb = Read-Host
        
        if ($createDb -eq 'y' -or $createDb -eq 'Y') {
            $createDbCommand = New-Object System.Data.SqlClient.SqlCommand("CREATE DATABASE TcpserverTms", $connection)
            $createDbCommand.ExecuteNonQuery()
            Write-Host "✅ 数据库 'TcpserverTms' 创建成功!" -ForegroundColor Green
        }
    } else {
        Write-Host "`n✅ 数据库 'TcpserverTms' 存在" -ForegroundColor Green
    }
    
    $connection.Close()
    
} catch {
    Write-Host "`n❌ 连接失败!" -ForegroundColor Red
    Write-Host "错误信息: $($_.Exception.Message)" -ForegroundColor Red
    
    # 提供故障排除建议
    Write-Host "`n故障排除建议:" -ForegroundColor Yellow
    Write-Host "1. 检查SQL Server服务是否运行: sc query MSSQLSERVER" -ForegroundColor White
    Write-Host "2. 检查TCP/IP协议是否启用: SQL Server Configuration Manager" -ForegroundColor White
    Write-Host "3. 检查sa用户是否启用: SQL Server Management Studio" -ForegroundColor White
    Write-Host "4. 检查防火墙设置: 确保1433端口开放" -ForegroundColor White
    Write-Host "5. 尝试使用Windows认证: 修改连接字符串为Integrated Security=true" -ForegroundColor White
}

}

Write-Host "`n测试完成。" -ForegroundColor Gray
