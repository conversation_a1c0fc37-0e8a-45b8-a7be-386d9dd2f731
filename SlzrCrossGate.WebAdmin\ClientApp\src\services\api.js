import axios from 'axios';

// 使用环境变量获取API基础URL
const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || '/api';

// 创建axios实例
const api = axios.create({
  baseURL: apiBaseUrl,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 获取存储的token
const getStoredToken = () => {
  return localStorage.getItem('token');
};

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 只有在没有手动设置Authorization头的情况下，才从存储中获取token
    if (!config.headers.Authorization) {
      const token = getStoredToken();
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    }
    console.log('请求:', config.method.toUpperCase(), config.url, config.data || config.params);
    console.log('Authorization头:', config.headers.Authorization ? '已设置' : '未设置');
    return config;
  },
  (error) => {
    console.error('请求错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    // 只在开发环境输出详细日志
    if (import.meta.env.DEV) {
      console.log('响应成功:', response.config.url, response.data);
    }

    // 对于blob响应，返回blob数据而不是response.data
    if (response.config.responseType === 'blob') {
      return response.data;
    }

    return response.data;
  },
  (error) => {
    // 只记录真正的错误，不记录业务逻辑错误（如删除已使用的文件类型）
    if (error.response?.status >= 500) {
      console.error('服务器错误:', error.config?.url, error.response?.status, error.response?.data);
    } else if (import.meta.env.DEV) {
      // 开发环境下记录所有错误
      console.error('响应错误:', error.config?.url, error.response?.status, error.response?.data);
    }

    // 处理401错误 - 自动跳转到登录页面
    if (error.response && error.response.status === 401) {
      // 清除token
      localStorage.removeItem('token');
      // 重定向到登录页
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);


// 认证相关API
export const authAPI = {
    login: (username, password) =>
        api.post('/auth/login', { username, password }),

    register: (data) =>
        api.post('/auth/register', data),

    verifyTwoFactorCode: (data) =>
        api.post('/auth/verify-code', data, {
            headers: data.TempToken ? {
                'Authorization': `Bearer ${data.TempToken}`
            } : undefined
        }),

    setupTwoFactor: (tempToken) =>
        api.post('/auth/setup-two-factor', { }, {
            headers: {
                'Authorization': `Bearer ${tempToken}`
            },
            // 添加错误处理
            validateStatus: function (status) {
                return status < 500; // 只有状态码小于500的响应会被解析为成功
            }
        }),

    confirmTwoFactor: (code, tempToken) =>
        api.post('/auth/confirm-two-factor', {
            Code: code,
            TempToken: tempToken
        }, {
            headers: {
                'Authorization': `Bearer ${tempToken}`
            },
            // 添加错误处理
            validateStatus: function (status) {
                return status < 500; // 只有状态码小于500的响应会被解析为成功
            }
        }),

    forgotPassword: (email) =>
        api.post('/auth/forgot-password', { email }),

    resetPassword: (data) =>
        api.post('/auth/reset-password', data),

    verifyCode: (username, code) =>
        api.post('/auth/verify-code', { username, code }),

    logout: () =>
        api.post('/auth/logout'),

    // 微信相关API
    getWechatLoginQrCode: () =>
        api.get('/auth/wechat-login'),

    checkWechatLoginStatus: (loginId) =>
        api.get(`/auth/wechat-login-status?loginId=${loginId}`),

    mockWechatScan: (data) =>
        api.post('/auth/wechat-mock-scan', data),

    mockWechatConfirm: (loginId) =>
        api.post('/auth/wechat-mock-confirm', { loginId }),

    bindWechat: (data) =>
        api.post('/auth/bind-wechat', data),

    unbindWechat: () =>
        api.post('/auth/unbind-wechat'),

    getWechatBinding: () =>
        api.get('/auth/wechat-binding'),

    // 启用或禁用双因素认证
    toggleTwoFactor: (enable, code = null) =>
        api.post('/auth/toggle-two-factor', { Enable: enable, Code: code }),

    // 强制密码更改
    forcePasswordChange: (data, tempToken) =>
        api.post('/auth/force-password-change', data, {
            headers: tempToken ? {
                'Authorization': `Bearer ${tempToken}`
            } : undefined
        }),
};

// 用户相关API
export const userAPI = {
  getUsers: (params) => api.get('/users', { params }),
  getUser: (id) => api.get(`/users/${id}`),
  createUser: (data) => api.post('/users', data),
  updateUser: (id, data) => api.put(`/users/${id}`, data),
  deleteUser: (id) => api.delete(`/users/${id}`),
  changePassword: (id, data) => api.post(`/users/${id}/change-password`, data),
  lockUser: (id, data) => api.post(`/users/${id}/lock`, data),
  unlockUser: (id) => api.post(`/users/${id}/unlock`),
  resetTwoFactor: (id) => api.post(`/users/${id}/reset-two-factor`),
  // 获取当前用户信息
  getCurrentUser: () => api.get('/users/CurrentUser'),
};

// 角色相关API
export const roleAPI = {
  getRoles: (params) => api.get('/roles', { params }),
  getRole: (id) => api.get(`/roles/${id}`),
  createRole: (data) => api.post('/roles', data),
  updateRole: (id, data) => api.put(`/roles/${id}`, data),
  deleteRole: (id) => api.delete(`/roles/${id}`),
  getUsersInRole: (id, params) => api.get(`/roles/${id}/users`, { params }),
};

// 商户相关API
export const merchantAPI = {
  getMerchants: (params) => api.get('/merchants', { params }),
  getMerchant: (id) => {
    if (!id) {
      console.error("尝试获取商户详情但ID为空");
      return Promise.reject(new Error("商户ID不能为空"));
    }
    return api.get(`/merchants/${id}`);
  },
  createMerchant: (data) => api.post('/merchants', data),
  updateMerchant: (id, data) => api.put(`/merchants/${id}`, data),
  deleteMerchant: (id) => api.delete(`/merchants/${id}`),
  getMerchantTerminals: (id, params) => api.get(`/merchants/${id}/terminals`, { params }),
  getMerchantUsers: (id, params) => api.get(`/merchants/${id}/users`, { params }),
  activateMerchant: (id) => api.post(`/merchants/${id}/activate`),
  deactivateMerchant: (id) => api.post(`/merchants/${id}/deactivate`),
};

// 终端相关API
export const terminalAPI = {
  getTerminals: (params) => api.get('/Terminals', { params }),
  getTerminal: (id) => api.get(`/Terminals/${id}`),
  updateTerminal: (id, data) => api.put(`/Terminals/${id}`, data),
  deleteTerminal: (id) => api.delete(`/Terminals/${id}`),
  getAllTerminalEvents: (params) => api.get('/Terminals/events', { params }),
  getTerminalEvents: (id, params) => api.get(`/Terminals/${id}/events`, { params }),
  getTerminalTypes: (params) => api.get('/TerminalTypes', { params }),
  createTerminalType: (data) => api.post('/TerminalTypes', data),
  updateTerminalType: (id, data) => api.put(`/TerminalTypes/${id}`, data),
  deleteTerminalType: (id) => api.delete(`/TerminalTypes/${id}`),
  sendMessage: (terminalIds, msgTypeCode, content, merchantId) =>
    api.post('/Terminals/SendMessage', { terminalIds, msgTypeCode, content, merchantId }),
  publishFile: (terminalIds, fileVerId) =>
    api.post('/Terminals/PublishFile', { terminalIds, fileVerId }),
  getTerminalStats: (params) => api.get(`/Terminals/Stats`,{ params }),
  getLineStats: (params) => api.get('/Terminals/LineStats', { params }),
  // 导出终端列表
  exportTerminals: (data) => api.post('/Terminals/export', data, {
    responseType: 'blob',
  }),
};

// 文件相关API
export const fileAPI = {
  getFileTypes: (params) => api.get('/FileTypes', { params }),
  getFileType: (id) => api.get(`/FileTypes/${id}`),
  createFileType: (data) => api.post('/FileTypes', data),
  updateFileType: (code, merchantId, data) => api.put(`/FileTypes/${code}/${merchantId}`, data),
  deleteFileType: (code, merchantId) => api.delete(`/FileTypes/${code}/${merchantId}`),

  getFileVersions: (params) => api.get('/FileVersions', { params }),
  getFileVersion: (id) => api.get(`/FileVersions/${id}`),
  uploadFile: (formData) => api.post('/FileVersions', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    timeout: 300000, // 5分钟超时，适合大文件上传
  }),
  deleteFileVersion: (id) => api.delete(`/FileVersions/${id}`),
  // 下载文件版本，使用响应类型blob接收二进制数据
  downloadFileVersion: (id) => api.get(`/FileVersions/download/${id}`, {
    responseType: 'blob',
  }),

  getFilePublishes: (params) => api.get('/FilePublish', { params }),
  getFilePublish: (id) => api.get(`/FilePublish/${id}`),
  createFilePublish: (data) => api.post('/FilePublish', data),
  updateFilePublish: (id, data) => api.put(`/FilePublish/${id}`, data),
  deleteFilePublish: (id) => api.delete(`/FilePublish/${id}`),

  getFilePublishHistory: (params) => api.get('/FilePublish/History', { params }),

  // 预约发布相关API
  getScheduledFilePublishes: (params) => api.get('/ScheduledFilePublish', { params }),
  getScheduledFilePublish: (id) => api.get(`/ScheduledFilePublish/${id}`),
  createScheduledFilePublish: (data) => api.post('/ScheduledFilePublish', data),
  updateScheduledFilePublish: (id, data) => api.put(`/ScheduledFilePublish/${id}`, data),
  cancelScheduledFilePublish: (id) => api.delete(`/ScheduledFilePublish/${id}`),

  // 获取所有文件类型(不分页，用于下拉框)
  getAllFileTypes: async (merchantId) => api.get('/FileTypes/all', { merchantId })
};

// 消息相关API
export const messageAPI = {
  getMessageTypes: (params) => api.get('/MessageTypes', { params }),
  getMessageType: (id) => api.get(`/MessageTypes/${id}`),
  createMessageType: (data) => api.post('/MessageTypes', data),
  updateMessageType: (code, merchantId, data) => api.put(`/MessageTypes/${code}/${merchantId}`, data),
  deleteMessageType: (code, merchantId) => api.delete(`/MessageTypes/${code}/${merchantId}`),

  // 获取所有消息类型(不分页，用于下拉框)
  getAllMessageTypes: async (merchantId) => {
    const params = merchantId ? { merchantId } : {};
    const response = await api.get('/MessageTypes/all', { params });
    // 处理不同的响应格式，确保返回一个数组
    if (response && Array.isArray(response)) {
      return response;
    } else if (response && Array.isArray(response.items)) {
      return response.items;
    } else if (response && typeof response === 'object') {
      // 尝试查找任何可能的数组属性
      for (const key in response) {
        if (Array.isArray(response[key])) {
          return response[key];
        }
      }
    }
    // 如果找不到任何有效数组，返回空数组
    console.warn('getAllMessageTypes: 无法从响应中提取有效数组', response);
    return [];
  },

  getMessages: (params) => api.get('/Messages', { params }),
  getMessage: (id) => api.get(`/Messages/${id}`),
  createMessage: (data) => api.post('/Messages', data),
  deleteMessage: (id) => api.delete(`/Messages/${id}`),
  getMessageStats: () => api.get('/Messages/Stats'),

  // 消息发送API
  sendMessageToTerminals: (data) => api.post('/Messages/Send', data),
  sendMessageToLine: (data) => api.post('/Messages/SendByLine', data),
  sendMessageToMerchant: (data) => api.post('/Messages/SendToMerchant', data),
};

// 仪表盘相关API
export const dashboardAPI = {
  getStats: () => api.get('/Dashboard/Stats'),
  getMerchantDashboard: () => api.get('/Dashboard/Merchant'),
  getPlatformDashboard: () => api.get('/Dashboard/Platform'),
  getServerLogs: (params) => api.get('/Dashboard/ServerLogs', { params }),
  // 添加新的方法以匹配实际调用路径
  getMerchantStats: (params) => api.get('/Dashboard/MerchantStats', { params }),
  getPlatformStats: () => api.get('/Dashboard/PlatformStats'),
  getSystemInfo: () => api.get('/Dashboard/SystemInfo'),
};

// 系统设置相关API
export const systemSettingsAPI = {
  getSettings: () => api.get('/SystemSettings'),
  updateSettings: (data) => api.put('/SystemSettings', data),
};

// 线路票价参数相关API
export const linePriceAPI = {
  // 线路票价基本信息
  getLinePrices: (params) => api.get('/LinePrice', { params }),
  getLinePrice: (id) => api.get(`/LinePrice/${id}`),
  createLinePrice: (data) => api.post('/LinePrice', data),
  updateLinePrice: (id, data) => api.put(`/LinePrice/${id}`, data),
  deleteLinePrice: (id) => api.delete(`/LinePrice/${id}`),

  // 线路票价版本
  getLinePriceVersions: (linePriceInfoId, params) =>
    api.get(`/LinePrice/${linePriceInfoId}/Versions`, { params }),
  getLinePriceVersion: (versionId) =>
    api.get(`/LinePrice/Versions/${versionId}`),
  createLinePriceVersion: (linePriceInfoId, data) =>
    api.post(`/LinePrice/${linePriceInfoId}/Versions`, data),
  updateLinePriceVersion: (versionId, data) =>
    api.put(`/LinePrice/Versions/${versionId}`, data),
  copyLinePriceVersion: (versionId) =>
    api.post(`/LinePrice/Versions/${versionId}/CopyCreate`),
  deleteLinePriceVersion: (versionId) =>
    api.delete(`/LinePrice/Versions/${versionId}`),

  // 搜索线路
  searchLinePrices: (params) =>
    api.get('/LinePrice/search', { params }),

  // 跨线路复制版本
  copyLinePriceVersionToOtherLines: (versionId, data) =>
    api.post(`/LinePrice/Versions/${versionId}/CopyToLines`, data),

  // 票价文件操作
  previewLinePriceFile: (versionId, data) =>
    api.post(`/LinePrice/Versions/${versionId}/Preview`, data),
  submitLinePriceVersion: (versionId, data) =>
    api.post(`/LinePrice/Versions/${versionId}/Submit`, data),
  publishLinePriceFile: (versionId, data) =>
    api.post(`/LinePrice/Versions/${versionId}/Publish`, data),

  // 字典配置
  getDictionaryConfig: (merchantId, dictionaryType) =>
    api.get(`/LinePrice/DictionaryConfig/${merchantId}/${dictionaryType}`, {
      params: { _t: Date.now() } // 添加时间戳防止缓存
    }),

  // 下载导入模板
  downloadTemplate: () => api.get('/LinePrice/template', {
    responseType: 'blob',
  }),
  // 批量导入线路参数
  importLinePrices: (formData) => api.post('/LinePrice/import', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    timeout: 120000, // 2分钟超时，适合批量导入
  }),
};

// 商户字典相关API
export const dictionaryAPI = {
  getDictionaries: (params) => api.get('/MerchantDictionary', { params }),
  getDictionary: (id) => api.get(`/MerchantDictionary/${id}`),
  createDictionary: (data) => api.post('/MerchantDictionary', data),
  updateDictionary: (id, data) => api.put(`/MerchantDictionary/${id}`, data),
  deleteDictionary: (id) => api.delete(`/MerchantDictionary/${id}`),
  getDictionaryTypes: (merchantId) => api.get(`/MerchantDictionary/Types/${merchantId}`),
  getDictionariesByType: (merchantId, dictionaryType) =>
    api.get(`/MerchantDictionary/ByType?merchantId=${merchantId}&dictionaryType=${dictionaryType}`),
};

// 银联终端密钥相关API
export const unionPayTerminalKeyAPI = {
  getUnionPayTerminalKeys: (params) => api.get('/UnionPayTerminalKeys', { params }),
  getUnionPayTerminalKey: (id) => api.get(`/UnionPayTerminalKeys/${id}`),
  createUnionPayTerminalKey: (data) => api.post('/UnionPayTerminalKeys', data),
  updateUnionPayTerminalKey: (id, data) => api.put(`/UnionPayTerminalKeys/${id}`, data),
  deleteUnionPayTerminalKey: (id) => api.delete(`/UnionPayTerminalKeys/${id}`),
  // 导入银联终端密钥
  importUnionPayTerminalKeys: (formData) => api.post('/UnionPayTerminalKeys/import', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    timeout: 120000, // 2分钟超时，适合批量导入
  }),
  // 下载导入模板
  downloadTemplate: () => api.get('/UnionPayTerminalKeys/template', {
    responseType: 'blob',
  }),
  // 导出银联终端密钥
  exportUnionPayTerminalKeys: (params) => api.get('/UnionPayTerminalKeys/export', {
    params,
    responseType: 'blob',
  }),
  // 解绑银联终端密钥
  unbindUnionPayTerminalKey: (id) => api.post(`/UnionPayTerminalKeys/${id}/unbind`),
};

// 未注册线路相关API
export const unregisteredLineAPI = {
  getUnregisteredLines: (params) => api.get('/UnregisteredLines', { params }),
  exportUnregisteredLines: (params) => api.get('/UnregisteredLines/export', {
    params,
    responseType: 'blob',
  }),
};

// 终端记录相关API
export const consumeDataAPI = {
  // 分页查询终端记录
  getConsumeData: (params) => api.get('/ConsumeData', { params }),
  // 导出终端记录为CSV文件
  exportConsumeData: (params) => api.get('/ConsumeData/export', {
    params,
    responseType: 'blob',
  }),
};

// 终端日志相关API
export const terminalLogAPI = {
  // 分页查询终端日志记录
  getTerminalLogs: (params) => api.get('/TerminalLog', { params }),
};

// 审计日志相关API
export const auditLogAPI = {
  // 获取登录日志
  getLoginLogs: (params) => api.get('/AuditLogs/login-logs', { params }),
  // 获取密码修改日志
  getPasswordChangeLogs: (params) => api.get('/AuditLogs/password-change-logs', { params }),
  // 获取操作日志
  getOperationLogs: (params) => api.get('/AuditLogs/operation-logs', { params }),
  // 获取审计日志统计信息
  getAuditLogStats: () => api.get('/AuditLogs/stats'),
  // 导出登录日志
  exportLoginLogs: (params) => api.get('/AuditLogs/login-logs/export', {
    params,
    responseType: 'blob',
  }),
  // 导出密码修改日志
  exportPasswordChangeLogs: (params) => api.get('/AuditLogs/password-change-logs/export', {
    params,
    responseType: 'blob',
  }),
  // 导出操作日志
  exportOperationLogs: (params) => api.get('/AuditLogs/operation-logs/export', {
    params,
    responseType: 'blob',
  }),
};

// 菜单管理API
export const menuAPI = {
  // 获取当前用户可见的菜单
  getUserMenus: () => api.get('/Menus/user-menus'),

  // 获取所有菜单（管理员专用）
  getAllMenus: () => api.get('/Menus'),

  // 获取菜单分组详情
  getMenuGroup: (id) => api.get(`/Menus/groups/${id}`),

  // 创建菜单分组
  createMenuGroup: (data) => api.post('/Menus/groups', data),

  // 更新菜单分组
  updateMenuGroup: (id, data) => api.put(`/Menus/groups/${id}`, data),

  // 删除菜单分组
  deleteMenuGroup: (id) => api.delete(`/Menus/groups/${id}`),

  // 获取菜单项详情
  getMenuItem: (id) => api.get(`/Menus/items/${id}`),

  // 创建菜单项
  createMenuItem: (data) => api.post('/Menus/items', data),

  // 更新菜单项
  updateMenuItem: (id, data) => api.put(`/Menus/items/${id}`, data),

  // 删除菜单项
  deleteMenuItem: (id) => api.delete(`/Menus/items/${id}`),

  // 初始化默认菜单数据
  initializeMenus: () => api.post('/Menus/initialize')
};

// 车辆管理API
export const vehicleAPI = {
  getVehicles: (params) => api.get('/vehicles', { params }),
  getVehicle: (id) => api.get(`/vehicles/${id}`),
  createVehicle: (data) => api.post('/vehicles', data),
  updateVehicle: (id, data) => api.put(`/vehicles/${id}`, data),
  deleteVehicle: (id) => api.delete(`/vehicles/${id}`),
  getVehicleByDevice: (deviceNo, merchantId) =>
    api.get(`/vehicles/by-device/${deviceNo}`, { params: { merchantId } }),
  getVehicleStats: (params) => api.get('/vehicles/stats', { params }),
  exportVehicles: (params) => api.get('/vehicles/export', {
    params,
    responseType: 'blob'
  }),
  // 下载导入模板
  downloadTemplate: () => api.get('/vehicles/template', {
    responseType: 'blob',
  }),
  // 批量导入车辆
  importVehicles: (formData) => api.post('/vehicles/import', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    timeout: 120000, // 2分钟超时，适合批量导入
  }),
};

// 票价折扣方案API
export const fareDiscountSchemeAPI = {
  // 获取方案列表
  getSchemes: (params) => api.get('/FareDiscountScheme', { params }),

  // 获取方案详情
  getScheme: (id) => api.get(`/FareDiscountScheme/${id}`),

  // 创建方案
  createScheme: (data) => api.post('/FareDiscountScheme', data),

  // 更新方案
  updateScheme: (id, data) => api.put(`/FareDiscountScheme/${id}`, data),

  // 删除方案
  deleteScheme: (id) => api.delete(`/FareDiscountScheme/${id}`),

  // 复制方案
  copyScheme: (id, newSchemeName) => api.post(`/FareDiscountScheme/${id}/copy`, JSON.stringify(newSchemeName), {
    headers: { 'Content-Type': 'application/json' }
  }),

  // 获取活跃方案列表（用于下拉选择）
  getActiveSchemes: () => api.get('/FareDiscountScheme/active'),

  // 提交方案生成版本
  submitScheme: (id, data) => api.post(`/FareDiscountScheme/${id}/submit`, data),

  // 获取方案版本历史
  getVersions: (id, params) => api.get(`/FareDiscountScheme/${id}/versions`, { params }),

  // 获取版本详情
  getVersion: (versionId) => api.get(`/FareDiscountScheme/versions/${versionId}`),

  // 预览版本文件
  previewVersion: (versionId) => api.post(`/FareDiscountScheme/versions/${versionId}/preview`),

  // 下载版本文件
  downloadVersion: (versionId) => api.get(`/FareDiscountScheme/versions/${versionId}/download`, {
    responseType: 'blob'
  })
};

// 权限管理API
export const permissionAPI = {
  // 获取用户权限
  getUserPermissions: () => api.get('/permissions/user-permissions'),

  // 获取功能配置列表
  getFeatureConfigs: () => api.get('/permissions/feature-configs'),

  // 批量更新权限配置
  batchUpdatePermissions: (updates) => api.post('/permissions/batch-update', { updates }),

  // 刷新权限缓存
  refreshCache: () => api.post('/permissions/refresh-cache'),

  // 检查权限
  checkPermissions: (featureKeys) => api.post('/permissions/check', { featureKeys }),

  // 初始化默认配置
  initializeDefaults: () => api.post('/permissions/initialize-defaults')
};

export default api;
